#!/bin/bash
# shellcheck disable=SC1091
cd "$(dirname "$0")" || exit
source .env
source /home/<USER>/freqtrade/.venv/bin/activate

freqtrade hyperopt \
    --datadir ${DATA_DIR} \
    --config ${CONFIG_FILE} \
    --timerange=${START_DATE}- \
    --strategy-path ${STRATEGY_PATH} \
    --strategy ${STRATEGY} \
    --hyperopt-loss OnlyProfitHyperOptLoss \
    --spaces buy \
    --epochs 100

# SharpeHyperOptLoss (推荐): 这是一个非常平衡的选择。它不仅考虑收益，还考虑了收益的波动性（风险）。夏普比率越高，说明在承担相同风险的情况下，策略获得的超额回报越高。这是衡量投资组合表现的黄金标准之一。
# SortinoHyperOptLoss: 与夏普比率类似，但它只惩罚“坏”的波动（即下跌的风险），而不惩罚“好”的波动（即向上的波动）。对于只关心下行风险的优化目标来说，这是一个很好的选择。
# OnlyProfitHyperOptLoss: 最简单粗暴的选择。它只关心最终的总利润，不考虑风险、回撤或交易次数。这可能导致优化出一个风险极高的策略。通常不建议在最终优化时使用，但可以用于早期快速探索。
# MaxDrawDownHyperOptLoss: 专注于最小化最大回撤。如果您的首要目标是控制资金曲线的回撤幅度，这是一个合适的选择。
