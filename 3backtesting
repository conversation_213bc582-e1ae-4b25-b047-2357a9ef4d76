#!/bin/bash
# shellcheck disable=SC1091

# 显示帮助信息
show_help() {
    cat <<EOF
用法: $0 [选项]

选项:
    -h, --help              显示此帮助信息
    --data-dir DIR          数据目录 (覆盖 DATA_DIR)
    --config-file FILE      配置文件 (覆盖 CONFIG_FILE)
    --start-date DATE       开始日期 (覆盖 START_DATE)
    --strategy-path PATH    策略路径 (覆盖 STRATEGY_PATH)
    --strategy NAME         策略名称 (覆盖 STRATEGY)
    --log-dir DIR          日志目录 (覆盖 LOG_DIR)
    --clean                 清理之前的回测结果
    --freqai-model MODEL   使用FreqAI模型

示例:
    $0 --strategy MyStrategy --start-date 20240101
    $0 --data-dir /path/to/data --config-file config/test.json
    $0 --strategy-path custom/strategies --strategy TestStrategy

EOF
}

# 切换到脚本目录
cd "$(dirname "$0")" || exit

# 加载 .env 文件中的默认值
source .env

# 默认设置
CLEAN_RESULTS=false
FREQAI_MODEL=""

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
    -h | --help)
        show_help
        exit 0
        ;;
    --data-dir)
        DATA_DIR="$2"
        shift 2
        ;;
    --config-file)
        CONFIG_FILE="$2"
        shift 2
        ;;
    --start-date)
        START_DATE="$2"
        shift 2
        ;;
    --strategy-path)
        STRATEGY_PATH="$2"
        shift 2
        ;;
    --strategy)
        STRATEGY="$2"
        shift 2
        ;;
    --log-dir)
        LOG_DIR="$2"
        shift 2
        ;;
    --clean)
        CLEAN_RESULTS=true
        shift
        ;;
    --freqai-model)
        FREQAI_MODEL="$2"
        shift 2
        ;;
    *)
        echo "未知选项: $1"
        echo "使用 --help 查看可用选项"
        exit 1
        ;;
    esac
done

# 激活虚拟环境
source /home/<USER>/freqtrade/.venv/bin/activate

if [[ -z "$START_DATE" ]]; then
    START_DATE=$(date -d "30 day ago" +%Y%m%d)
fi

# 显示当前配置
echo "=== 回测配置 ==="
echo "数据目录: ${DATA_DIR}"
echo "配置文件: ${CONFIG_FILE}"
echo "开始日期: ${START_DATE}"
echo "策略路径: ${STRATEGY_PATH}"
echo "策略名称: ${STRATEGY}"
echo "日志目录: ${LOG_DIR}"
echo "清理结果: ${CLEAN_RESULTS}"
if [[ -n "$FREQAI_MODEL" ]]; then
    echo "FreqAI模型: ${FREQAI_MODEL}"
fi
echo "================"

# 清理之前的回测结果（如果启用）
if [[ "$CLEAN_RESULTS" == "true" ]]; then
    echo "清理之前的回测结果..."
    rm -rf user_data/backtest_results
fi

# 构建freqtrade命令
freqtrade_cmd=(
    freqtrade backtesting
    --datadir "${DATA_DIR}"
    --config "${CONFIG_FILE}"
    --timerange="${START_DATE}-"
    --strategy-path "${STRATEGY_PATH}"
    --strategy "${STRATEGY}"
    --breakdown day week month
)

# 如果指定了FreqAI模型，添加到命令中
if [[ -n "$FREQAI_MODEL" ]]; then
    freqtrade_cmd+=(--freqaimodel "${FREQAI_MODEL}")
fi

# 执行回测
"${freqtrade_cmd[@]}"

freqtrade backtesting-show >user_data/strategies/${STRATEGY}.txt
