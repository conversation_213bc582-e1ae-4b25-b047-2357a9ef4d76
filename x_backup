#!/usr/bin/env bash
# shellcheck disable=SC2035

cd "$(dirname "$0")" || exit
script_path=$(pwd)
script_dirname=$(basename "$script_path")

tar -czv -f "backup_linux_${script_dirname}_$(date '+%Y%m%d-%H%M%S').tar.gz" \
    --exclude=.git \
    --exclude=.DS_Store \
    --exclude=__pycache__ \
    --exclude=backup_*.tar.gz \
    --exclude=tensorflow \
    \
    --exclude=user_data/backtest_results \
    --exclude=user_data/data \
    --exclude=user_data/hyperopt_results \
    --exclude=user_data/hyperopts \
    --exclude=user_data/logs \
    --exclude=user_data/models \
    --exclude=user_data/notebooks \
    --exclude=user_data/plot \
    --exclude=user_data/strategies/sample_strategy.py \
    --exclude=user_data/tradesv3.sqlite* \
    --exclude=user_data/*.bak \
    \
    --exclude=LICENSE \
    --exclude=README.md \
    * .[!.]*
