#!/bin/bash
# shellcheck disable=SC1091

source .env
source /Users/<USER>/freqAI/freqtrade/.venv/bin/activate

# 删除旧日志文件
find "$LOG_DIR" -type f \( -size -100k -o -mtime +4 \) | sort -r | tail -n +6 | while read -r file; do
    rm "$file"
    echo "Deleted: $file"
done
# 生成日志文件名
LOG_FILE="$LOG_DIR/$(date +"%Y%m%d-%H%M%S").log"

freqtrade trade \
    --config "${CONFIG_FILE}" \
    --logfile "${LOG_FILE}" \
    --db-url "${DB_URL}"
