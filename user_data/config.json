{"trading_mode": "futures", "margin_mode": "isolated", "max_open_trades": 100, "stake_currency": "USDT", "stake_amount": "unlimited", "dry_run_wallet": 20000, "tradable_balance_ratio": 0.99, "fiat_display_currency": "USD", "dry_run": true, "force_entry_enable": true, "initial_state": "running", "internals": {"process_throttle_secs": 5}, "cancel_open_orders_on_exit": false, "unfilledtimeout": {"entry": 10, "exit": 30}, "entry_pricing": {"price_side": "other", "use_order_book": true, "order_book_top": 1, "price_last_balance": 0.0, "check_depth_of_market": {"enabled": false, "bids_to_ask_delta": 1}}, "exit_pricing": {"price_side": "other", "use_order_book": true, "order_book_top": 1}, "exchange": {"name": "binance", "sandbox": false, "key": "", "secret": "", "enable_ws": true, "ccxt_config": {"enableRateLimit": false, "httpsProxy": "http://localhost:3213", "wsProxy": "http://localhost:3213"}, "ccxt_async_config": {"enableRateLimit": false}, "pair_whitelist": ["BTC/USDT:USDT", "ETH/USDT:USDT", "ETC/USDT:USDT", "FIL/USDT:USDT", "LTC/USDT:USDT", "XRP/USDT:USDT", "DOGE/USDT:USDT", "ADA/USDT:USDT", "TON/USDT:USDT", "LINK/USDT:USDT", "XLM/USDT:USDT", "AVAX/USDT:USDT", "HBAR/USDT:USDT", "DOT/USDT:USDT", "XMR/USDT:USDT", "UNI/USDT:USDT", "APT/USDT:USDT", "NEAR/USDT:USDT", "ICP/USDT:USDT", "AAVE/USDT:USDT", "ATOM/USDT:USDT", "TAO/USDT:USDT", "ENA/USDT:USDT", "POL/USDT:USDT", "TIA/USDT:USDT", "ALGO/USDT:USDT", "ARB/USDT:USDT", "OP/USDT:USDT", "MKR/USDT:USDT", "EOS/USDT:USDT", "FET/USDT:USDT", "JUP/USDT:USDT", "QNT/USDT:USDT", "STX/USDT:USDT", "MOVE/USDT:USDT", "WLD/USDT:USDT", "GRT/USDT:USDT", "THETA/USDT:USDT", "IMX/USDT:USDT", "XTZ/USDT:USDT", "GALA/USDT:USDT", "SAND/USDT:USDT", "CRV/USDT:USDT", "ZEC/USDT:USDT", "IOTA/USDT:USDT", "FLOW/USDT:USDT", "JTO/USDT:USDT", "CAKE/USDT:USDT", "ENS/USDT:USDT", "PENDLE/USDT:USDT", "JASMY/USDT:USDT", "DYDX/USDT:USDT", "MANA/USDT:USDT", "KAVA/USDT:USDT", "AXS/USDT:USDT", "CFX/USDT:USDT", "RSR/USDT:USDT", "STRK/USDT:USDT", "COMP/USDT:USDT", "EGLD/USDT:USDT", "CHZ/USDT:USDT", "WIF/USDT:USDT", "W/USDT:USDT", "AR/USDT:USDT", "NEO/USDT:USDT", "TWT/USDT:USDT", "LAYER/USDT:USDT", "ZRO/USDT:USDT", "MINA/USDT:USDT", "DASH/USDT:USDT", "GLM/USDT:USDT", "SNX/USDT:USDT", "BLUR/USDT:USDT", "KAITO/USDT:USDT", "ZK/USDT:USDT", "CKB/USDT:USDT", "SUPER/USDT:USDT", "ASTR/USDT:USDT", "KAIA/USDT:USDT", "1INCH/USDT:USDT", "ZIL/USDT:USDT", "QTUM/USDT:USDT"], "pair_blacklist": []}, "pairlists": [{"method": "StaticPairList"}, {"method": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "shuffle_frequency": "candle", "seed": 42}], "api_server": {"enabled": true, "listen_ip_address": "0.0.0.0", "listen_port": 8080, "verbosity": "error", "enable_openapi": false, "jwt_secret_key": "a741e55c8f8a4f83b754875586fee12d9478f342c19743ab8724c127bb5346ef", "ws_token": "", "CORS_origins": ["http://frequi"], "username": "cz", "password": "juedui"}}