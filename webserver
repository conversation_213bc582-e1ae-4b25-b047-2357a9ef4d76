#!/bin/bash
# shellcheck disable=SC1091
cd "$(dirname "$0")" || exit
source .env
source /home/<USER>/freqtrade/.venv/bin/activate

session_name="webserver"

if tmux ls | grep "$session_name:" >/dev/null; then
    tmux -u attach -t "$session_name"
else
    tmux -u new-session -s "$session_name" -- \
        freqtrade \
        webserver \
        --datadir ${DATA_DIR} \
        --config ${CONFIG_FILE}
fi
